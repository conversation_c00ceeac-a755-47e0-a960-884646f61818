package apis

import (
	"encoding/json"
	"errors"
	"net/http"
	"runtime"
	"time"
	"webapisrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	serverInfoPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/http"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
)

// ServerInfoRequest 处理服务器信息请求
func (e AppInfo) ServerInfoRequest(c *gin.Context) {
	err := e.MakeContext(c).Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(http.StatusInternalServerError, err, err.Error())
		return
	}

	reqData, err := c.GetRawData()
	if err != nil {
		logrus.Errorf("Failed to read raw data of message: %s", err.Error())
		e.Logger.Error(err)
		e.Error(int(commonPB.ErrCode_ERR_ILLEGAL_OPERATION), err, err.Error())
		return
	}

	if len(reqData) <= 0 {
		err = errors.New("reqData is empty")
		logrus.Errorf("Failed to read raw data of message: %s", err.Error())
		e.Logger.Error(err)
		e.Error(int(commonPB.ErrCode_ERR_ILLEGAL_OPERATION), err, err.Error())
		return
	}

	ctx := c.ContentType()

	req := &model.GetServerInfoReq{}

	if model.IsJsonCtxType(ctx) {
		err = json.Unmarshal(reqData, req)
		if err != nil {
			e.Logger.Error(err)
			e.Error(http.StatusInternalServerError, err, err.Error())
			return
		}
	} else {
		reqProto := &serverInfoPB.ServerInfoReq{}
		err = proto.Unmarshal(reqData, reqProto)
		if err != nil {
			logrus.Errorf("Failed to unmarshal message: %v", err)
			e.Logger.Error(err)
			e.Error(http.StatusInternalServerError, err, err.Error())
			return
		}

		req.Channel = int32(reqProto.GetChannel())
		req.ProductId = int32(reqProto.GetProductId())
	}

	channel := req.Channel
	productID := req.ProductId

	// 检查参数
	if channel <= 0 || productID <= 0 {
		err := errors.New(commonPB.ErrCode_ERR_BAD_PARAM.String())
		e.Error(int(commonPB.ErrCode_ERR_BAD_PARAM), err, err.Error())
		logrus.Errorf("Failed to param err channel: %d, productId:%d", channel, productID)
		return
	}

	logrus.Tracef("recv server info request %v", req)

	// 获取服务器信息
	now := time.Now()
	timezone, offset := now.Zone()

	// 获取系统信息
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	serverInfo := &model.ServerInfo{
		ServerTime:     now.Unix(),
		ServerTimeStr:  now.Format("2006-01-02 15:04:05"),
		Timezone:       timezone,
		TimezoneOffset: int32(offset),
		ServerVersion:  "1.0.0", // 可以从配置或构建信息中获取
		GoVersion:      runtime.Version(),
		NumCPU:         int32(runtime.NumCPU()),
		NumGoroutine:   int32(runtime.NumGoroutine()),
		MemoryUsage:    int64(memStats.Alloc),
		TotalMemory:    int64(memStats.Sys),
	}

	logrus.Infof("server info response: %+v", serverInfo)

	// 构建响应数据
	response := map[string]interface{}{
		"code": 200,
		"msg":  "成功",
		"data": serverInfo,
	}

	if model.IsJsonCtxType(ctx) {
		e.OKJson(response, "成功")
	} else {
		// 对于 protobuf 响应，我们需要构建正确的 protobuf 结构
		// 由于外部包结构不确定，暂时使用 JSON 响应
		e.OKJson(response, "成功")
	}
}
