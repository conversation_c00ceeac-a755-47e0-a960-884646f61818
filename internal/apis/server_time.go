package apis

import (
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"net/http"
	"time"
	"webapisrv/internal/model"
)

func (e AppInfo) GetServerTime(c *gin.Context) {
	err := e.<PERSON>(c).Errors
	if err != nil {
		e.Logger.Error(err)
		e.<PERSON>rror(http.StatusInternalServerError, err, err.Error())
		return
	}

	req := &model.ServerTimeReq{}
	if err = c.ShouldBind(&req); err != nil {
		logrus.Errorf("Failed to unmarshal message: %v", err)
		e.Logger.Error(err)
		e.<PERSON>rror(http.StatusInternalServerError, err, err.Error())
		return
	}
	if req.ProductId != 1 || req.Channel == 0 {
		err = errors.New("parameter error")
		e.<PERSON>rror(http.StatusInternalServerError, err, err.<PERSON><PERSON><PERSON>())
		return
	}

	now := time.Now()
	name, _ := now.Zone()
	rsp := model.ServerTimeRes{
		DateTime:  now.Format("2006-01-02 15:04:05"),
		Timestamp: now.Unix(),
		Timezone:  name,
	}

	e.<PERSON><PERSON>son(rsp, "成功")
}
