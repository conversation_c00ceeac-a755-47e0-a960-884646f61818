package apis

import (
	"encoding/json"
	"errors"
	"net/http"
	"webapisrv/internal/model"
	api "webapisrv/internal/sdk"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	appInfoPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/http"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
)

type AppInfo struct {
	api.ContextApi
}

func (e AppInfo) AppInfoRequest(c *gin.Context) {
	err := e.MakeContext(c).Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(http.StatusInternalServerError, err, err.Error())
		return
	}

	reqData, err := c.GetRawData()
	if err != nil {
		logrus.Errorf("Failed to read raw data of message: %s", err.Error())
		e.Logger.Error(err)
		e.Error(int(commonPB.ErrCode_ERR_ILLEGAL_OPERATION), err, err.Error())
		return
	}

	if len(reqData) <= 0 {
		err = errors.New("reqData is empty")
		logrus.Errorf("Failed to read raw data of message: %s", err.Error())
		e.Logger.Error(err)
		e.Error(int(commonPB.ErrCode_ERR_ILLEGAL_OPERATION), err, err.Error())
		return
	}

	ctx := c.ContentType()

	req := &model.GetAppInfoReq{}

	if model.IsJsonCtxType(ctx) {
		err = json.Unmarshal(reqData, req)
		if err != nil {
			e.Logger.Error(err)
			e.Error(http.StatusInternalServerError, err, err.Error())
			return
		}
	} else {
		reqProto := &appInfoPB.AppInfoReq{}
		err = proto.Unmarshal(reqData, reqProto)
		if err != nil {
			logrus.Errorf("Failed to unmarshal message: %v", err)
			e.Logger.Error(err)
			e.Error(http.StatusInternalServerError, err, err.Error())
			return
		}

		req.AppLanguage = reqProto.GetAppLanguage()
		req.AppVersion = reqProto.GetAppVersion()
		req.Channel = int32(reqProto.GetChannel())
		req.Platform = int32(reqProto.GetPlatform())
		req.ProductId = int32(reqProto.GetProductId())
	}

	// clientVersion := req.GetAppVersion()
	// language := req.GetAppLanguage()
	channel := req.Channel
	plt := req.Platform
	productID := req.ProductId

	// 检查参数
	if channel <= 0 || plt <= 0 || productID <= 0 {
		err := errors.New(commonPB.ErrCode_ERR_BAD_PARAM.String())
		e.Error(int(commonPB.ErrCode_ERR_BAD_PARAM), err, err.Error())
		logrus.Errorf("Failed to param err channel: %d, plt:%d", channel, plt)
		return
	}

	logrus.Tracef("recv version %v, ", req)

	// appUpdateInfo
	appUpdateInfoObj := cmodel.GetAppUpdateInfo(consul_config.WithProduct(int(productID)), consul_config.WithChannel(int32(channel)))

	// appResourceInfo
	appResourceInfoObj := cmodel.GetAppResourceInfo(consul_config.WithProduct(int(productID)), consul_config.WithChannel(int32(channel)))

	if appUpdateInfoObj == nil || appResourceInfoObj == nil {
		err := errors.New(commonPB.ErrCode_ERR_CONF_ERROR.String())
		e.Error(int(commonPB.ErrCode_ERR_CONF_ERROR), err, err.Error())
		return
	}

	infoUpdate := commonPB.AppUpdateInfo{
		Version:       appUpdateInfoObj.Version,
		IsPrompt:      appUpdateInfoObj.IsPrompt,
		IsForce:       appUpdateInfoObj.IsForce,
		DownloadUrl:   appUpdateInfoObj.DownloadUrl,
		TipMsgKey:     appUpdateInfoObj.TipMsgKey,
		MinAppVersion: appUpdateInfoObj.MinAppVersion,
	}

	infoRes := commonPB.AppResourceInfo{
		ConfigMd5:   appResourceInfoObj.ConfigMd5,
		RemoteMd5:   appResourceInfoObj.RemoteMd5,
		RemoteForce: appResourceInfoObj.RemoteForce,
		PatchForce:  appResourceInfoObj.PatchForce,
	}

	// appAddressInfo
	appAddressInfoObj := cmodel.GetAppAddressInfo(consul_config.WithProduct(int(productID)), consul_config.WithChannel(int32(channel)))

	infoAddress := commonPB.AppAddressInfo{
		SrvUri:       appAddressInfoObj.SrvUri,
		CdnHost:      appAddressInfoObj.CdnHost,
		ResourceUri:  appAddressInfoObj.ResourceUri,
		ConfigUri:    appAddressInfoObj.ConfigUri,
		FbShareUri:   appAddressInfoObj.FbShareUri,
		LogUploadUri: appAddressInfoObj.LogUploadUri,
	}

	// 审核策略信息
	auditStrategy :=  GetAuditStrategy(int(productID), int32(channel), req.AppVersion)
	if auditStrategy != nil { 
		infoAddress.SrvUri = auditStrategy.AuditSrvUrl
		infoAddress.ConfigUri = auditStrategy.AuditConfig
	}

	logrus.Infof("updateInfo:%v, addrInfo:%v, resInfo:%v", appUpdateInfoObj, appAddressInfoObj, appResourceInfoObj)

	rspRet := &commonPB.Result{
		Code: commonPB.ErrCode_ERR_SUCCESS,
		Desc: commonPB.ErrCode_ERR_SUCCESS.String(),
	}

	rsp := &appInfoPB.AppInfoRsp{
		Ret:          rspRet,
		InfoUpdate:   &infoUpdate,
		InfoResource: &infoRes,
		InfoAddress:  &infoAddress,
	}

	if model.IsJsonCtxType(ctx) {
		e.OKJson(rsp, "成功")
	} else {
		e.OKProtobuf(rsp, "成功")
	}
}
