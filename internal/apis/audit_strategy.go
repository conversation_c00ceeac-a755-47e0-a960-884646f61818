package apis

import (
	"git.keepfancy.xyz/back-end/frameworks/kit/version"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"github.com/sirupsen/logrus"
)

// GetAuditStrategy 获取审核策略信息 没有返回空
func GetAuditStrategy(productId int, channel int32, cliVersion string) *cmodel.AuditStrategy {
	// 查询审核策略配置
	auditConf := cmodel.GetAuditStrategy(consul_config.WithProduct(productId), consul_config.WithChannel(channel))
	if auditConf == nil || !auditConf.AuditOpen { 
		return nil
	}

	// 判断是否大于等于配置版本号
	if version.Compare(cliVersion, auditConf.AuditVersion) >= 0 {
		
		logrus.Debugf("productId: %d, channel: %d, cliVersion: %s, to audit:%+v", productId, channel, cliVersion, *auditConf)

		return auditConf
	}

	return nil
}
