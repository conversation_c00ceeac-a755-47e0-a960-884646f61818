package apis

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"webapisrv/internal/model"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestServerInfoRequest(t *testing.T) {
	// 设置 Gin 为测试模式
	gin.SetMode(gin.TestMode)

	// 创建测试用的请求数据
	req := model.GetServerInfoReq{
		Channel:   1,
		ProductId: 1,
	}

	// 将请求数据转换为 JSON
	reqData, err := json.Marshal(req)
	assert.NoError(t, err)

	// 创建 HTTP 请求
	httpReq, err := http.NewRequest("POST", "/webapi/getServerInfo", bytes.NewBuffer(reqData))
	assert.NoError(t, err)
	httpReq.Header.Set("Content-Type", "application/json")

	// 创建响应记录器
	w := httptest.NewRecorder()

	// 创建 Gin 上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = httpReq

	// 创建 API 实例并调用方法
	api := &AppInfo{}
	api.ServerInfoRequest(c)

	// 验证响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 解析响应数据
	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 验证响应结构
	assert.Contains(t, response, "code")
	assert.Contains(t, response, "data")
	assert.Equal(t, float64(200), response["code"])
}

func TestServerInfoRequestWithInvalidParams(t *testing.T) {
	// 设置 Gin 为测试模式
	gin.SetMode(gin.TestMode)

	// 创建无效的请求数据
	req := model.GetServerInfoReq{
		Channel:   0, // 无效的 channel
		ProductId: 1,
	}

	// 将请求数据转换为 JSON
	reqData, err := json.Marshal(req)
	assert.NoError(t, err)

	// 创建 HTTP 请求
	httpReq, err := http.NewRequest("POST", "/webapi/getServerInfo", bytes.NewBuffer(reqData))
	assert.NoError(t, err)
	httpReq.Header.Set("Content-Type", "application/json")

	// 创建响应记录器
	w := httptest.NewRecorder()

	// 创建 Gin 上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = httpReq

	// 创建 API 实例并调用方法
	api := &AppInfo{}
	api.ServerInfoRequest(c)

	// 验证响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 解析响应数据
	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 验证错误响应
	assert.Contains(t, response, "code")
	assert.NotEqual(t, float64(200), response["code"])
}
