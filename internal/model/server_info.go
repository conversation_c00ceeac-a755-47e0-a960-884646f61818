package model

// GetServerInfoReq 获取服务器信息请求
type GetServerInfoReq struct {
	Channel   int32 `json:"channel"`
	ProductId int32 `json:"product_id"`
}

// ServerInfo 服务器信息
type ServerInfo struct {
	ServerTime     int64  `json:"server_time"`      // 服务器时间戳（秒）
	ServerTimeStr  string `json:"server_time_str"`  // 格式化的服务器时间
	Timezone       string `json:"timezone"`         // 时区名称
	TimezoneOffset int32  `json:"timezone_offset"`  // 时区偏移（秒）
	ServerVersion  string `json:"server_version"`   // 服务器版本
	GoVersion      string `json:"go_version"`       // Go 版本
	NumCPU         int32  `json:"num_cpu"`          // CPU 核心数
	NumGoroutine   int32  `json:"num_goroutine"`    // 当前 Goroutine 数量
	MemoryUsage    int64  `json:"memory_usage"`     // 当前内存使用量（字节）
	TotalMemory    int64  `json:"total_memory"`     // 总内存（字节）
}
