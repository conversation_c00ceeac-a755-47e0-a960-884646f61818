package model

const (
	AppInfoCtxJsType = "application/json"         //json类型(json解析)
	AppInfoCtxOsType = "application/octet-stream" //os类型(proto解析)
)

func IsJsonCtxType(typeStr string) bool {
	return typeStr == AppInfoCtxJsType
}

func IsOsCtxType(typeStr string) bool {
	return typeStr == AppInfoCtxOsType
}

// GetAppInfoReq 查询
type GetAppInfoReq struct {
	AppVersion  string `json:"app_version"`
	AppLanguage string `json:"app_language"`
	Channel     int32  `json:"channel"`
	ProductId   int32  `json:"product_id"`
	Platform    int32  `json:"platform"`
}