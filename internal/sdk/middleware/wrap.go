package middleware

import (
	"fmt"
	"io"
	"net/http"
	"strings"
	"webapisrv/config"

	crypto "git.keepfancy.xyz/back-end/frameworks/lib/crypto"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

type responeWriterWrap struct {
	gin.ResponseWriter
}

func (w *responeWriterWrap) Write(b []byte) (int, error) {
	fmt.Println("write", string(b))
	encryptKey := viper.GetString(config.KEY_API_SECRET)
	encrypted, err := crypto.AesEncrypt(b, encryptKey)
	if err != nil {
		return 0, err
	}
	b = encrypted

	return w.ResponseWriter.Write(b)
}

// EncryptionMiddleware 加密中间件
func EncryptionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// encryptKey := viper.GetString(config.KEY_API_SECRET)
		// 替换修改模式
		c.Writer = &responeWriterWrap{c.Writer}

		// var bodyBytes []byte
		// if c.Request.Body != nil {
		// 	bodyBytes, _ = c.GetRawData()
		// }

		// encrypted, err := crypto.AesEncrypt(bodyBytes, encryptKey)
		// if err != nil {
		// 	c.AbortWithStatus(http.StatusBadRequest)
		// 	return
		// }

		// c.Request.Body = io.NopCloser(strings.NewReader(string(encrypted)))
		c.Next()
	}
}

// DecryptMiddleware 解密中间件
func DecryptMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		var bodyBytes []byte
		if c.Request.Body != nil {
			bodyBytes, _ = c.GetRawData()
		}
		encryptKey := viper.GetString(config.KEY_API_SECRET)
		decrypted, err := crypto.AesDecrypt(bodyBytes, encryptKey)
		if err != nil {
			c.AbortWithStatus(http.StatusBadRequest)
			logrus.Warnf("Failed to decrypt message: %s", err.Error())
			return
		}
		c.Request.Body = io.NopCloser(strings.NewReader(string(decrypted)))
		c.Next()
	}
}
