package api

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/golang/protobuf/proto"
	"github.com/sirupsen/logrus"
	"log"
	"webapisrv/internal/sdk/response"
)

var DefaultLanguage = "zh-CN"

type ContextApi struct {
	Context *gin.Context
	Logger  *logrus.Logger
	Errors  error
}

func (e *ContextApi) AddError(err error) {
	if e.Errors == nil {
		e.Errors = err
	} else if err != nil {
		e.Logger.Error(err)
		e.Errors = fmt.Errorf("%v; %w", e.Errors, err)
	}
}

// MakeContext 设置http上下文
func (e *ContextApi) MakeContext(c *gin.Context) *ContextApi {
	e.Context = c
	return e
}

// Bind 参数校验
func (e *ContextApi) Bind(d interface{}, bindings ...binding.Binding) *ContextApi {
	var err error
	if len(bindings) == 0 {
		bindings = constructor.GetBindingForGin(d)
	}

	for i := range bindings {
		if bindings[i] == nil {
			err = e.Context.ShouldBindUri(d)
		} else {
			err = e.Context.ShouldBindWith(d, bindings[i])
		}

		if err != nil && err.Error() == "EOF" {
			e.Logger.Warn("request body is not present anymore. ")
			err = nil
			continue
		}

		if err != nil {
			e.AddError(err)
			break
		}
	}

	return e
}

// Error 通常错误数据处理
func (e *ContextApi) Error(code int, err error, msg string) {
	response.Error(e.Context, code, err, msg)
}

// OKProtobuf 通常成功数据处理 Protobuf
func (e *ContextApi) OKProtobuf(pbData proto.Message, msg string) {
	// 将消息转换为字节数组
	data, err := proto.Marshal(pbData)
	if err != nil {
		log.Fatalf("Failed to marshal message: %v", err)
	}

	response.OKProtobuf(e.Context, data, msg)
}

// OKJson 通常成功数据处理 Json
func (e *ContextApi) OKJson(data interface{}, msg string) {
	response.OKJson(e.Context, data, msg)
}
