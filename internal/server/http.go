package server

import (
	"git.keepfancy.xyz/back-end/frameworks/kit/ginx"
	"webapisrv/internal/apis"
	"webapisrv/internal/sdk/middleware"
)

func RegisterAppInfoRouter() {
	r := ginx.GetGinEngine()
	api := &apis.AppInfo{}
	// infoGroup := r.Group("/webapi").Use(middleware.AuthCheckRole())
	infoGroup := r.Group("/webapi").
		Use(middleware.EncryptionMiddleware()).
		Use(middleware.DecryptMiddleware())
	{
		infoGroup.POST("/getAppInfo",  api.AppInfoRequest)
		infoGroup.GET("/getServerTime", api.GetServerTime)
	}

}
