# Server Info API 文档

## 概述

`server_info` API 提供服务器的基本信息，包括时间、版本、系统资源等信息。

## 接口信息

- **URL**: `/webapi/getServerInfo`
- **方法**: `POST`
- **Content-Type**: `application/json` 或 `application/octet-stream`

## 请求参数

### JSON 格式请求

```json
{
    "channel": 1,
    "product_id": 1
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| channel | int32 | 是 | 渠道ID，必须大于0 |
| product_id | int32 | 是 | 产品ID，必须大于0 |

## 响应格式

### 成功响应

```json
{
    "code": 200,
    "msg": "成功",
    "data": {
        "server_time": 1754558074,
        "server_time_str": "2025-08-07 17:14:34",
        "timezone": "CST",
        "timezone_offset": 28800,
        "server_version": "1.0.0",
        "go_version": "go1.23.3",
        "num_cpu": 10,
        "num_goroutine": 2,
        "memory_usage": 2633104,
        "total_memory": 13714696
    }
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| server_time | int64 | 服务器时间戳（秒） |
| server_time_str | string | 格式化的服务器时间 |
| timezone | string | 时区名称 |
| timezone_offset | int32 | 时区偏移（秒） |
| server_version | string | 服务器版本 |
| go_version | string | Go 版本 |
| num_cpu | int32 | CPU 核心数 |
| num_goroutine | int32 | 当前 Goroutine 数量 |
| memory_usage | int64 | 当前内存使用量（字节） |
| total_memory | int64 | 总内存（字节） |

### 错误响应

```json
{
    "code": 400,
    "msg": "ERR_BAD_PARAM",
    "status": "error"
}
```

## 使用示例

### cURL 示例

```bash
curl -X POST http://localhost:21101/webapi/getServerInfo \
  -H "Content-Type: application/json" \
  -d '{
    "channel": 1,
    "product_id": 1
  }'
```

### JavaScript 示例

```javascript
fetch('/webapi/getServerInfo', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    channel: 1,
    product_id: 1
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 参数错误（channel 或 product_id 无效） |
| 500 | 服务器内部错误 |

## 注意事项

1. `channel` 和 `product_id` 参数必须大于 0
2. 接口支持 JSON 和 Protobuf 两种请求格式
3. 内存使用量和总内存以字节为单位
4. 时区偏移以秒为单位，正值表示东时区，负值表示西时区
