# server
rpc_server_name: webapi
rpc_port: 11105

# 开发模式 （release debug test）
env_mode: debug

# 端口号
http_port: 21105
tcp_port: 31105

# 日志相关
log_level: trace
log_write: true
log_dir: ../../logs/webapi
log_json: false

redis_addr: 192.168.1.58:6379
redis_passwd: 8888

redis_list:
  gateway: 
    addr: 192.168.1.58:6379
    passwd: 8888

  player:
    addr: 192.168.1.58:6379
    passwd: 8888

consul_addr: 192.168.1.58:8500

rpc_server_tags: debug

jwt:
  timeout: 876010
  secret: fancygame

api_secret: IgkibX71IEf382PT