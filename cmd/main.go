package main

import (
	"context"
	"runtime"
	"webapisrv/config"
	"webapisrv/internal/server"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/driver"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

type webapiService struct {
	Name string
	Ctx  context.Context
}

func (w *webapiService) Init() error {
	w.Ctx = context.Background()

	// 设置gin 引擎处理Http服务
	// ginx.GetRawHttpEngine().Handler = ginx.GetGinEngine()

	w.Name = viper.GetString(dict.ConfigRpcServerName)
	logrus.Infoln(w.Name + "服务Init")
	
	envMode := viper.GetString(dict.ConfigGinEnvMode)
	gin.SetMode(envMode)
	server.RegisterAppInfoRouter()

	// 初始化配置
	err := config.InitConfig()
	if err != nil {
		logrus.Errorf("初始化配置失败:%+v", err)
		return err
	}


	return nil
}

func (w *webapiService) Start() error {
	return nil
}

func (w *webapiService) Stop() error {
	return nil
}

func (w *webapiService) ForceStop() error {
	return nil
}

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU() * 2)
	random.NewInitSource()

	driver.Run(&webapiService{})
}
