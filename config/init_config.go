package config

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/config"
)

func InitConfig() error {
	serviceConfig := config.NewServiceConfig()

	serviceConfig.Register("InitAppAddressInfoCfg", cmodel.InitAppAddressInfoCfg)   // 初始化app地址信息
	serviceConfig.Register("InitAppUpdateInfoCfg", cmodel.InitAppUpdateInfoCfg)     // 初始化app更新信息
	serviceConfig.Register("InitAppResourceInfoCfg", cmodel.InitAppResourceInfoCfg) // 初始化app资源信息
	serviceConfig.Register("InitAuditStrategyCfg", cmodel.InitAuditStrategyCfg)     // 初始化审核策略信息

	return serviceConfig.ExecuteAll()
}
